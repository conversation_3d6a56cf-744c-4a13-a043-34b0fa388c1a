{"request": {"id": "f2906238-e0ca-4686-a326-ffb51aa1527a", "scan_type": "sbom", "target": ".", "options": {"include_dev_dependencies": false, "max_depth": 10, "timeout": 300, "include_transitive": true, "exclude_patterns": ["**/node_modules/**", "**/target/**", "**/.git/**"], "include_patterns": ["**/*"], "output_formats": ["json", "cyclonedx"], "enable_vulnerability_scan": true, "enable_license_scan": true}, "metadata": {}}, "status": "completed", "started_at": "2025-08-25T07:21:45.799393477Z", "completed_at": "2025-08-25T07:21:45.860618379Z", "duration": {"secs": 0, "nanos": 61000000}, "software_components": [{"name": "anyhow", "version": "1.0.99", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "argon2", "version": "0.5.3", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "async-trait", "version": "0.1.89", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "axum", "version": "0.7.9", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "base64", "version": "0.22.1", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "bincode", "version": "1.3.3", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "blake3", "version": "1.8.2", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "chrono", "version": "0.4.41", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "clap", "version": "4.5.45", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "color-eyre", "version": "0.6.5", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "config", "version": "0.14.1", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "csv", "version": "1.3.1", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "cyclonedx-bom", "version": "0.4.3", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "diesel", "version": "2.2.12", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "diesel_migrations", "version": "2.2.0", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "ed25519-<PERSON><PERSON><PERSON>", "version": "2.2.0", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "flate2", "version": "1.1.2", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "futures", "version": "0.3.31", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "governor", "version": "0.6.3", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "handlebars", "version": "4.5.0", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "hex", "version": "0.4.3", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "hyper", "version": "1.7.0", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "itertools", "version": "0.12.1", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "jsonwebtoken", "version": "9.3.1", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "lapin", "version": "2.5.4", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "lazy_static", "version": "1.5.0", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "merk<PERSON><PERSON>", "version": "0.23.0", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "metrics", "version": "0.22.4", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "metrics-prometheus", "version": "0.6.0", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "once_cell", "version": "1.21.3", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "printpdf", "version": "0.6.0", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "prometheus", "version": "0.13.4", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "rand", "version": "0.9.2", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "rayon", "version": "1.11.0", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "redis", "version": "0.24.0", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "regex", "version": "1.11.1", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "reqwest", "version": "0.11.27", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "ring", "version": "0.17.14", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "semver", "version": "1.0.26", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "serde", "version": "1.0.219", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "serde_json", "version": "1.0.143", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "serde_yaml", "version": "0.9.34+deprecated", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "sha2", "version": "0.10.9", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "spdx", "version": "0.10.9", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "sqlx", "version": "0.7.4", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "tar", "version": "0.4.44", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "tempfile", "version": "3.21.0", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "tera", "version": "1.20.0", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "thiserror", "version": "2.0.16", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "time", "version": "0.3.41", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "tokio", "version": "1.47.1", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "tokio-cron-scheduler", "version": "0.9.4", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "toml", "version": "0.9.5", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "tower", "version": "0.5.2", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "tower-http", "version": "0.5.2", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "tracing", "version": "0.1.41", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "tracing-appender", "version": "0.2.3", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "tracing-subscriber", "version": "0.3.19", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "url", "version": "2.5.7", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "2.1.3", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "u<PERSON><PERSON>a", "version": "4.2.3", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "utoipa-swagger-ui", "version": "6.0.0", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "uuid", "version": "1.18.0", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "validator", "version": "0.16.1", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "walkdir", "version": "2.5.0", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "wkhtmltopdf", "version": "0.4.0", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "xml-rs", "version": "0.8.27", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "zip", "version": "0.6.6", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "runtime", "metadata": {}}, {"name": "criterion", "version": "0.5.1", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "development", "metadata": {}}, {"name": "mockall", "version": "0.12.1", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "development", "metadata": {}}, {"name": "pretty_assertions", "version": "1.4.1", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "development", "metadata": {}}, {"name": "proptest", "version": "1.7.0", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "development", "metadata": {}}, {"name": "rstest", "version": "0.18.2", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "development", "metadata": {}}, {"name": "serial_test", "version": "3.2.0", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "development", "metadata": {}}, {"name": "testcontainers", "version": "0.15.0", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "development", "metadata": {}}, {"name": "tokio-test", "version": "0.4.4", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "development", "metadata": {}}, {"name": "wiremock", "version": "0.5.22", "package_type": "crate", "package_manager": "cargo", "description": null, "homepage": null, "repository": null, "license": null, "dependencies": [], "file_path": "./Cargo.toml", "hash": null, "scope": "development", "metadata": {}}], "hardware_components": [], "repository_info": null, "dependency_tree": null, "vulnerabilities": [], "licenses": [], "issues": [], "metadata": {}}