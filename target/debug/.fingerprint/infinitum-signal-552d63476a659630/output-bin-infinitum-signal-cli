{"$message_type":"diagnostic","message":"unused imports: `VulnerabilitySeverity` and `VulnerabilitySource`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":452,"byte_end":473,"line_start":10,"line_end":10,"column_start":89,"column_end":110,"is_primary":true,"text":[{"text":"    vulnerability::{VulnerabilityOrchestrator, VulnerabilityRequest, AssessmentOptions, VulnerabilitySeverity, VulnerabilitySource},","highlight_start":89,"highlight_end":110}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/cli.rs","byte_start":475,"byte_end":494,"line_start":10,"line_end":10,"column_start":112,"column_end":131,"is_primary":true,"text":[{"text":"    vulnerability::{VulnerabilityOrchestrator, VulnerabilityRequest, AssessmentOptions, VulnerabilitySeverity, VulnerabilitySource},","highlight_start":112,"highlight_end":131}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":450,"byte_end":494,"line_start":10,"line_end":10,"column_start":87,"column_end":131,"is_primary":true,"text":[{"text":"    vulnerability::{VulnerabilityOrchestrator, VulnerabilityRequest, AssessmentOptions, VulnerabilitySeverity, VulnerabilitySource},","highlight_start":87,"highlight_end":131}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `VulnerabilitySeverity` and `VulnerabilitySource`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:10:89\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    vulnerability::{VulnerabilityOrchestrator, VulnerabilityRequest, AssessmentOptions, VulnerabilitySeverity, VulnerabilitySource},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":573,"byte_end":578,"line_start":13,"line_end":13,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"use tracing::{error, info, warn};","highlight_start":15,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":573,"byte_end":580,"line_start":13,"line_end":13,"column_start":15,"column_end":22,"is_primary":true,"text":[{"text":"use tracing::{error, info, warn};","highlight_start":15,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `error`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:13:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{error, info, warn};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `enable_security_analysis`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":10516,"byte_end":10540,"line_start":374,"line_end":374,"column_start":38,"column_end":62,"is_primary":true,"text":[{"text":"        ScanCommands::Hbom { target, enable_security_analysis, extract_files } => {","highlight_start":38,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":10516,"byte_end":10540,"line_start":374,"line_end":374,"column_start":38,"column_end":62,"is_primary":true,"text":[{"text":"        ScanCommands::Hbom { target, enable_security_analysis, extract_files } => {","highlight_start":38,"highlight_end":62}],"label":null,"suggested_replacement":"enable_security_analysis: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `enable_security_analysis`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:374:38\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m374\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        ScanCommands::Hbom { target, enable_security_analysis, extract_files } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `enable_security_analysis: _`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `extract_files`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":10542,"byte_end":10555,"line_start":374,"line_end":374,"column_start":64,"column_end":77,"is_primary":true,"text":[{"text":"        ScanCommands::Hbom { target, enable_security_analysis, extract_files } => {","highlight_start":64,"highlight_end":77}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":10542,"byte_end":10555,"line_start":374,"line_end":374,"column_start":64,"column_end":77,"is_primary":true,"text":[{"text":"        ScanCommands::Hbom { target, enable_security_analysis, extract_files } => {","highlight_start":64,"highlight_end":77}],"label":null,"suggested_replacement":"extract_files: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `extract_files`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:374:64\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m374\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        ScanCommands::Hbom { target, enable_security_analysis, extract_files } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `extract_files: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `include_history`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":11303,"byte_end":11318,"line_start":391,"line_end":391,"column_start":38,"column_end":53,"is_primary":true,"text":[{"text":"        ScanCommands::Repo { target, include_history, scan_secrets } => {","highlight_start":38,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":11303,"byte_end":11318,"line_start":391,"line_end":391,"column_start":38,"column_end":53,"is_primary":true,"text":[{"text":"        ScanCommands::Repo { target, include_history, scan_secrets } => {","highlight_start":38,"highlight_end":53}],"label":null,"suggested_replacement":"include_history: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `include_history`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:391:38\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m391\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        ScanCommands::Repo { target, include_history, scan_secrets } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `include_history: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `scan_secrets`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":11320,"byte_end":11332,"line_start":391,"line_end":391,"column_start":55,"column_end":67,"is_primary":true,"text":[{"text":"        ScanCommands::Repo { target, include_history, scan_secrets } => {","highlight_start":55,"highlight_end":67}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":11320,"byte_end":11332,"line_start":391,"line_end":391,"column_start":55,"column_end":67,"is_primary":true,"text":[{"text":"        ScanCommands::Repo { target, include_history, scan_secrets } => {","highlight_start":55,"highlight_end":67}],"label":null,"suggested_replacement":"scan_secrets: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `scan_secrets`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:391:55\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m391\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        ScanCommands::Repo { target, include_history, scan_secrets } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `scan_secrets: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `sbom`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":12316,"byte_end":12320,"line_start":421,"line_end":421,"column_start":32,"column_end":36,"is_primary":true,"text":[{"text":"        VulnCommands::Assess { sbom, target, sources, severity_threshold, include_epss } => {","highlight_start":32,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":12316,"byte_end":12320,"line_start":421,"line_end":421,"column_start":32,"column_end":36,"is_primary":true,"text":[{"text":"        VulnCommands::Assess { sbom, target, sources, severity_threshold, include_epss } => {","highlight_start":32,"highlight_end":36}],"label":null,"suggested_replacement":"sbom: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `sbom`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:421:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m421\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        VulnCommands::Assess { sbom, target, sources, severity_threshold, include_epss } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `sbom: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `target`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":12322,"byte_end":12328,"line_start":421,"line_end":421,"column_start":38,"column_end":44,"is_primary":true,"text":[{"text":"        VulnCommands::Assess { sbom, target, sources, severity_threshold, include_epss } => {","highlight_start":38,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":12322,"byte_end":12328,"line_start":421,"line_end":421,"column_start":38,"column_end":44,"is_primary":true,"text":[{"text":"        VulnCommands::Assess { sbom, target, sources, severity_threshold, include_epss } => {","highlight_start":38,"highlight_end":44}],"label":null,"suggested_replacement":"target: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `target`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:421:38\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m421\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        VulnCommands::Assess { sbom, target, sources, severity_threshold, include_epss } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `target: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `sources`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":12330,"byte_end":12337,"line_start":421,"line_end":421,"column_start":46,"column_end":53,"is_primary":true,"text":[{"text":"        VulnCommands::Assess { sbom, target, sources, severity_threshold, include_epss } => {","highlight_start":46,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":12330,"byte_end":12337,"line_start":421,"line_end":421,"column_start":46,"column_end":53,"is_primary":true,"text":[{"text":"        VulnCommands::Assess { sbom, target, sources, severity_threshold, include_epss } => {","highlight_start":46,"highlight_end":53}],"label":null,"suggested_replacement":"sources: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `sources`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:421:46\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m421\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        VulnCommands::Assess { sbom, target, sources, severity_threshold, include_epss } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `sources: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `severity_threshold`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":12339,"byte_end":12357,"line_start":421,"line_end":421,"column_start":55,"column_end":73,"is_primary":true,"text":[{"text":"        VulnCommands::Assess { sbom, target, sources, severity_threshold, include_epss } => {","highlight_start":55,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":12339,"byte_end":12357,"line_start":421,"line_end":421,"column_start":55,"column_end":73,"is_primary":true,"text":[{"text":"        VulnCommands::Assess { sbom, target, sources, severity_threshold, include_epss } => {","highlight_start":55,"highlight_end":73}],"label":null,"suggested_replacement":"severity_threshold: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `severity_threshold`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:421:55\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m421\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        VulnCommands::Assess { sbom, target, sources, severity_threshold, include_epss } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `severity_threshold: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `include_epss`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":12359,"byte_end":12371,"line_start":421,"line_end":421,"column_start":75,"column_end":87,"is_primary":true,"text":[{"text":"        VulnCommands::Assess { sbom, target, sources, severity_threshold, include_epss } => {","highlight_start":75,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":12359,"byte_end":12371,"line_start":421,"line_end":421,"column_start":75,"column_end":87,"is_primary":true,"text":[{"text":"        VulnCommands::Assess { sbom, target, sources, severity_threshold, include_epss } => {","highlight_start":75,"highlight_end":87}],"label":null,"suggested_replacement":"include_epss: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `include_epss`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:421:75\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m421\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        VulnCommands::Assess { sbom, target, sources, severity_threshold, include_epss } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `include_epss: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `format`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":13164,"byte_end":13170,"line_start":437,"line_end":437,"column_start":39,"column_end":45,"is_primary":true,"text":[{"text":"        VulnCommands::Report { input, format, include_remediation } => {","highlight_start":39,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":13164,"byte_end":13170,"line_start":437,"line_end":437,"column_start":39,"column_end":45,"is_primary":true,"text":[{"text":"        VulnCommands::Report { input, format, include_remediation } => {","highlight_start":39,"highlight_end":45}],"label":null,"suggested_replacement":"format: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `format`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:437:39\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m437\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        VulnCommands::Report { input, format, include_remediation } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `format: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `include_remediation`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":13172,"byte_end":13191,"line_start":437,"line_end":437,"column_start":47,"column_end":66,"is_primary":true,"text":[{"text":"        VulnCommands::Report { input, format, include_remediation } => {","highlight_start":47,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":13172,"byte_end":13191,"line_start":437,"line_end":437,"column_start":47,"column_end":66,"is_primary":true,"text":[{"text":"        VulnCommands::Report { input, format, include_remediation } => {","highlight_start":47,"highlight_end":66}],"label":null,"suggested_replacement":"include_remediation: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `include_remediation`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:437:47\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m437\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        VulnCommands::Report { input, format, include_remediation } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `include_remediation: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `force`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":13422,"byte_end":13427,"line_start":442,"line_end":442,"column_start":30,"column_end":35,"is_primary":true,"text":[{"text":"        VulnCommands::Sync { force, sources } => {","highlight_start":30,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":13422,"byte_end":13427,"line_start":442,"line_end":442,"column_start":30,"column_end":35,"is_primary":true,"text":[{"text":"        VulnCommands::Sync { force, sources } => {","highlight_start":30,"highlight_end":35}],"label":null,"suggested_replacement":"force: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `force`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:442:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m442\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        VulnCommands::Sync { force, sources } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `force: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `sources`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":13429,"byte_end":13436,"line_start":442,"line_end":442,"column_start":37,"column_end":44,"is_primary":true,"text":[{"text":"        VulnCommands::Sync { force, sources } => {","highlight_start":37,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":13429,"byte_end":13436,"line_start":442,"line_end":442,"column_start":37,"column_end":44,"is_primary":true,"text":[{"text":"        VulnCommands::Sync { force, sources } => {","highlight_start":37,"highlight_end":44}],"label":null,"suggested_replacement":"sources: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `sources`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:442:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m442\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        VulnCommands::Sync { force, sources } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `sources: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `scan_results`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":14025,"byte_end":14037,"line_start":461,"line_end":461,"column_start":65,"column_end":77,"is_primary":true,"text":[{"text":"        ComplianceCommands::Generate { framework, organization, scan_results, format, executive_summary } => {","highlight_start":65,"highlight_end":77}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":14025,"byte_end":14037,"line_start":461,"line_end":461,"column_start":65,"column_end":77,"is_primary":true,"text":[{"text":"        ComplianceCommands::Generate { framework, organization, scan_results, format, executive_summary } => {","highlight_start":65,"highlight_end":77}],"label":null,"suggested_replacement":"scan_results: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `scan_results`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:461:65\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m461\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        ComplianceCommands::Generate { framework, organization, scan_results, format, executive_summary } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `scan_results: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `input`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":16080,"byte_end":16085,"line_start":504,"line_end":504,"column_start":40,"column_end":45,"is_primary":true,"text":[{"text":"        ComplianceCommands::Validate { input, framework } => {","highlight_start":40,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":16080,"byte_end":16085,"line_start":504,"line_end":504,"column_start":40,"column_end":45,"is_primary":true,"text":[{"text":"        ComplianceCommands::Validate { input, framework } => {","highlight_start":40,"highlight_end":45}],"label":null,"suggested_replacement":"input: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `input`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:504:40\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m504\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        ComplianceCommands::Validate { input, framework } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `input: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `cli`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":16918,"byte_end":16921,"line_start":524,"line_end":524,"column_start":5,"column_end":8,"is_primary":true,"text":[{"text":"    cli: &Cli,","highlight_start":5,"highlight_end":8}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":16918,"byte_end":16921,"line_start":524,"line_end":524,"column_start":5,"column_end":8,"is_primary":true,"text":[{"text":"    cli: &Cli,","highlight_start":5,"highlight_end":8}],"label":null,"suggested_replacement":"_cli","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `cli`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:524:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m524\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    cli: &Cli,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_cli`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `blockchain`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":16980,"byte_end":16990,"line_start":527,"line_end":527,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    let blockchain = BlockchainOrchestrator::new(config.blockchain.clone())?;","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":16980,"byte_end":16990,"line_start":527,"line_end":527,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    let blockchain = BlockchainOrchestrator::new(config.blockchain.clone())?;","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":"_blockchain","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `blockchain`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:527:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m527\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let blockchain = BlockchainOrchestrator::new(config.blockchain.clone())?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_blockchain`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `data`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":17130,"byte_end":17134,"line_start":530,"line_end":530,"column_start":49,"column_end":53,"is_primary":true,"text":[{"text":"        BlockchainCommands::Commit { data_type, data, metadata } => {","highlight_start":49,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":17130,"byte_end":17134,"line_start":530,"line_end":530,"column_start":49,"column_end":53,"is_primary":true,"text":[{"text":"        BlockchainCommands::Commit { data_type, data, metadata } => {","highlight_start":49,"highlight_end":53}],"label":null,"suggested_replacement":"data: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `data`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:530:49\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m530\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        BlockchainCommands::Commit { data_type, data, metadata } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `data: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `metadata`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":17136,"byte_end":17144,"line_start":530,"line_end":530,"column_start":55,"column_end":63,"is_primary":true,"text":[{"text":"        BlockchainCommands::Commit { data_type, data, metadata } => {","highlight_start":55,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":17136,"byte_end":17144,"line_start":530,"line_end":530,"column_start":55,"column_end":63,"is_primary":true,"text":[{"text":"        BlockchainCommands::Commit { data_type, data, metadata } => {","highlight_start":55,"highlight_end":63}],"label":null,"suggested_replacement":"metadata: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `metadata`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:530:55\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m530\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        BlockchainCommands::Commit { data_type, data, metadata } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `metadata: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `output`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":17382,"byte_end":17388,"line_start":535,"line_end":535,"column_start":43,"column_end":49,"is_primary":true,"text":[{"text":"        BlockchainCommands::Proof { data, output } => {","highlight_start":43,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":17382,"byte_end":17388,"line_start":535,"line_end":535,"column_start":43,"column_end":49,"is_primary":true,"text":[{"text":"        BlockchainCommands::Proof { data, output } => {","highlight_start":43,"highlight_end":49}],"label":null,"suggested_replacement":"output: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `output`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:535:43\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m535\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        BlockchainCommands::Proof { data, output } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `output: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `claims`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":17659,"byte_end":17665,"line_start":540,"line_end":540,"column_start":68,"column_end":74,"is_primary":true,"text":[{"text":"        BlockchainCommands::Credential { credential_type, subject, claims } => {","highlight_start":68,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":17659,"byte_end":17665,"line_start":540,"line_end":540,"column_start":68,"column_end":74,"is_primary":true,"text":[{"text":"        BlockchainCommands::Credential { credential_type, subject, claims } => {","highlight_start":68,"highlight_end":74}],"label":null,"suggested_replacement":"claims: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `claims`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:540:68\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m540\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        BlockchainCommands::Credential { credential_type, subject, claims } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `claims: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `verify_type`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":17943,"byte_end":17954,"line_start":545,"line_end":545,"column_start":46,"column_end":57,"is_primary":true,"text":[{"text":"        BlockchainCommands::Verify { record, verify_type } => {","highlight_start":46,"highlight_end":57}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":17943,"byte_end":17954,"line_start":545,"line_end":545,"column_start":46,"column_end":57,"is_primary":true,"text":[{"text":"        BlockchainCommands::Verify { record, verify_type } => {","highlight_start":46,"highlight_end":57}],"label":null,"suggested_replacement":"verify_type: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `verify_type`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:545:46\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m545\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        BlockchainCommands::Verify { record, verify_type } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `verify_type: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `cli`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":18231,"byte_end":18234,"line_start":557,"line_end":557,"column_start":5,"column_end":8,"is_primary":true,"text":[{"text":"    cli: &Cli,","highlight_start":5,"highlight_end":8}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":18231,"byte_end":18234,"line_start":557,"line_end":557,"column_start":5,"column_end":8,"is_primary":true,"text":[{"text":"    cli: &Cli,","highlight_start":5,"highlight_end":8}],"label":null,"suggested_replacement":"_cli","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `cli`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:557:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m557\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    cli: &Cli,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_cli`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `target`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":18421,"byte_end":18427,"line_start":563,"line_end":563,"column_start":38,"column_end":44,"is_primary":true,"text":[{"text":"        DbCommands::Migrate { check, target } => {","highlight_start":38,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":18421,"byte_end":18427,"line_start":563,"line_end":563,"column_start":38,"column_end":44,"is_primary":true,"text":[{"text":"        DbCommands::Migrate { check, target } => {","highlight_start":38,"highlight_end":44}],"label":null,"suggested_replacement":"target: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `target`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:563:38\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m563\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        DbCommands::Migrate { check, target } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `target: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `compression`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":19312,"byte_end":19323,"line_start":582,"line_end":582,"column_start":38,"column_end":49,"is_primary":true,"text":[{"text":"        DbCommands::Backup { output, compression } => {","highlight_start":38,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":19312,"byte_end":19323,"line_start":582,"line_end":582,"column_start":38,"column_end":49,"is_primary":true,"text":[{"text":"        DbCommands::Backup { output, compression } => {","highlight_start":38,"highlight_end":49}],"label":null,"suggested_replacement":"compression: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `compression`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:582:38\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m582\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        DbCommands::Backup { output, compression } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `compression: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `config`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/cli.rs","byte_start":20004,"byte_end":20010,"line_start":605,"line_end":605,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: Arc<Config>,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/cli.rs","byte_start":20004,"byte_end":20010,"line_start":605,"line_end":605,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: Arc<Config>,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":"_config","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `config`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/cli.rs:605:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m605\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config: Arc<Config>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_config`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"28 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 28 warnings emitted\u001b[0m\n\n"}
