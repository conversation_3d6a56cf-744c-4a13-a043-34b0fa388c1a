{"rustc": 12013579709055016942, "features": "[\"rustls-connector\", \"rustls-native-certs\"]", "declared_features": "[\"default\", \"native-tls\", \"openssl\", \"rustls\", \"rustls-connector\", \"rustls-native-certs\", \"rustls-webpki-roots-certs\", \"vendored-openssl\"]", "target": 11924301528678068555, "profile": 5676177281124120482, "path": 15715064353650526474, "deps": [[8606274917505247608, "tracing", false, 3170405985263080523], [11096876330329401515, "amq_protocol_uri", false, 2444313655219222875], [17059544261156971941, "tcp_stream", false, 17867880373075350405]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/amq-protocol-tcp-be4774467fc4c7d7/dep-lib-amq_protocol_tcp", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}