{"rustc": 12013579709055016942, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9241925498456048256, "build_script_build", false, 5967145341616638171]], "local": [{"RerunIfChanged": {"output": "release/build/blake3-15afc17a0109df2c/output", "paths": ["c/blake3_avx512_x86-64_unix.S", "c/cmake", "c/dependencies", "c/blake3_sse2_x86-64_windows_msvc.asm", "c/blake3_avx2_x86-64_unix.S", "c/blake3_avx2_x86-64_windows_gnu.S", "c/blake3_avx2_x86-64_windows_msvc.asm", "c/example_tbb.c", "c/blake3_sse2_x86-64_windows_gnu.S", "c/CMakePresets.json", "c/blake3_avx512_x86-64_windows_gnu.S", "c/blake3_sse41.c", "c/blake3.c", "c/README.md", "c/blake3_sse41_x86-64_windows_gnu.S", "c/libblake3.pc.in", "c/blake3_dispatch.c", "c/CMakeLists.txt", "c/blake3-config.cmake.in", "c/blake3_avx512_x86-64_windows_msvc.asm", "c/blake3_avx2.c", "c/blake3_sse2_x86-64_unix.S", "c/blake3_neon.c", "c/blake3_tbb.cpp", "c/blake3_sse2.c", "c/blake3_impl.h", "c/Makefile.testing", "c/example.c", "c/main.c", "c/.giti<PERSON>re", "c/blake3_avx512.c", "c/blake3_portable.c", "c/test.py", "c/blake3_sse41_x86-64_unix.S", "c/blake3.h", "c/blake3_sse41_x86-64_windows_msvc.asm"]}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_NO_NEON", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PREFER_INTRINSICS", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PREFER_INTRINSICS", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_NEON", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_NO_NEON", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}