{"rustc": 12013579709055016942, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"async\", \"default\", \"futures\", \"std\"]", "target": 8369499057004385739, "profile": 5676177281124120482, "path": 17143400955093251576, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/weezl-fdf52afcc72be78a/dep-lib-weezl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}