{"rustc": 12013579709055016942, "features": "[]", "declared_features": "[\"actix\", \"actix-web\", \"axum\", \"axum-ex\", \"compression\", \"debug-embed\", \"deterministic-timestamps\", \"hex\", \"include-exclude\", \"include-flate\", \"interpolate-folder-path\", \"mime-guess\", \"mime_guess\", \"poem\", \"poem-ex\", \"rocket\", \"salvo\", \"salvo-ex\", \"tokio\", \"warp\", \"warp-ex\"]", "target": 3385222681681722461, "profile": 5676177281124120482, "path": 8364148719750857334, "deps": [[5409933923103361951, "rust_embed_utils", false, 7180175461549009758], [11693977163544003021, "rust_embed_impl", false, 1052078157872661688], [15622660310229662834, "walkdir", false, 11397936340233780915]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rust-embed-70ba60d594f0267f/dep-lib-rust_embed", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}