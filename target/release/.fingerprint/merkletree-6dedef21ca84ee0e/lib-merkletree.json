{"rustc": 12013579709055016942, "features": "[]", "declared_features": "[]", "target": 1653214005644274254, "profile": 5676177281124120482, "path": 4915891773632287130, "deps": [[3292989461258298505, "positioned_io", false, 12749191203732524610], [5986029879202738730, "log", false, 1338623378557747062], [9529943735784919782, "arrayref", false, 8419956431109366598], [9689903380558560274, "serde", false, 13024165464168184619], [10504454274054532777, "memmap2", false, 14414735832366758559], [11207653606310558077, "anyhow", false, 16851365717453167817], [12176524612222138457, "tempfile", false, 17960579698839322346], [14807177696891839338, "rayon", false, 8426651873092457213], [17001665395952474378, "typenum", false, 10674387613531845882]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/merkletree-6dedef21ca84ee0e/dep-lib-merkletree", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}