{"rustc": 12013579709055016942, "features": "[\"crossbeam-epoch\", \"crossbeam-utils\", \"handles\", \"hashbrown\", \"num_cpus\", \"registry\"]", "declared_features": "[\"ahash\", \"aho-corasick\", \"crossbeam-epoch\", \"crossbeam-utils\", \"debugging\", \"default\", \"handles\", \"hashbrown\", \"indexmap\", \"layer-filter\", \"layer-router\", \"layers\", \"num_cpus\", \"ordered-float\", \"quanta\", \"radix_trie\", \"recency\", \"registry\", \"sketches-ddsketch\", \"summary\"]", "target": 13028150694916642802, "profile": 5676177281124120482, "path": 5506900801490365875, "deps": [[2357570525450087091, "num_cpus", false, 3593453779997916163], [3528074118530651198, "crossbeam_epoch", false, 44827408433823398], [4468123440088164316, "crossbeam_utils", false, 2522778885720062016], [4801984952432540513, "metrics", false, 17938909774493214921], [13018563866916002725, "hashbrown", false, 2823516866341032114]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/metrics-util-2e350a338450839c/dep-lib-metrics_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}