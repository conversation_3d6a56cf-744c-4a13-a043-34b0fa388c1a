{"rustc": 12013579709055016942, "features": "[\"rustls-native-certs\"]", "declared_features": "[\"amq-protocol-codegen\", \"codegen\", \"codegen-internal\", \"default\", \"native-tls\", \"openssl\", \"rustls\", \"rustls-native-certs\", \"rustls-webpki-roots-certs\", \"vendored-openssl\", \"verbose-errors\"]", "target": 12659125817264579830, "profile": 5676177281124120482, "path": 17559277081917400164, "deps": [[2985572863888970315, "amq_protocol_types", false, 8742314776488219387], [4886105269790530060, "cookie_factory", false, 4452604092170098011], [6502365400774175331, "nom", false, 1839500977407781021], [7048981225526245511, "build_script_build", false, 9692121951579452622], [9689903380558560274, "serde", false, 13024165464168184619], [11096876330329401515, "amq_protocol_uri", false, 2444313655219222875], [12404004217544788841, "amq_protocol_tcp", false, 6922666936202381525]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/amq-protocol-b7d945bd2e113f08/dep-lib-amq_protocol", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}