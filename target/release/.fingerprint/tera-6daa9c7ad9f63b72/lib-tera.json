{"rustc": 12013579709055016942, "features": "[\"builtins\", \"chrono\", \"chrono-tz\", \"default\", \"humansize\", \"percent-encoding\", \"rand\", \"slug\", \"urlencode\"]", "declared_features": "[\"builtins\", \"chrono\", \"chrono-tz\", \"date-locale\", \"default\", \"humansize\", \"percent-encoding\", \"preserve_order\", \"rand\", \"slug\", \"urlencode\"]", "target": 10510219667581518171, "profile": 5676177281124120482, "path": 14353435224076939840, "deps": [[1142496670751353492, "unic_segment", false, 13319690935772828302], [2631894480810835227, "chrono_tz", false, 4889051963336123865], [3221585212778410572, "pest", false, 17364785319602160367], [4352886507220678900, "serde_json", false, 13071536196104845035], [6593674146359544692, "humansize", false, 9453994320184006568], [6803352382179706244, "percent_encoding", false, 9365665524907602399], [9451456094439810778, "regex", false, 9093491637583685841], [9689903380558560274, "serde", false, 13024165464168184619], [9897246384292347999, "chrono", false, 5263219938714622616], [9901698829223861929, "globwalk", false, 5226702265255614835], [12719040206398185542, "slug", false, 1964914056305953318], [13050370547047919637, "pest_derive", false, 18446396910787801934], [13208667028893622512, "rand", false, 11109878790133241354], [17917672826516349275, "lazy_static", false, 8514575940895466465]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tera-6daa9c7ad9f63b72/dep-lib-tera", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}