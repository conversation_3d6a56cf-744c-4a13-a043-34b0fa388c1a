{"rustc": 12013579709055016942, "features": "[\"__common\", \"balance\", \"buffer\", \"default\", \"discover\", \"filter\", \"full\", \"futures-core\", \"futures-util\", \"hdrhistogram\", \"hedge\", \"indexmap\", \"limit\", \"load\", \"load-shed\", \"log\", \"make\", \"pin-project\", \"pin-project-lite\", \"rand\", \"ready-cache\", \"reconnect\", \"retry\", \"slab\", \"spawn-ready\", \"steer\", \"timeout\", \"tokio\", \"tokio-util\", \"tracing\", \"util\"]", "declared_features": "[\"__common\", \"balance\", \"buffer\", \"default\", \"discover\", \"filter\", \"full\", \"futures-core\", \"futures-util\", \"hdrhistogram\", \"hedge\", \"indexmap\", \"limit\", \"load\", \"load-shed\", \"log\", \"make\", \"pin-project\", \"pin-project-lite\", \"rand\", \"ready-cache\", \"reconnect\", \"retry\", \"slab\", \"spawn-ready\", \"steer\", \"timeout\", \"tokio\", \"tokio-stream\", \"tokio-util\", \"tracing\", \"util\"]", "target": 3486700084251681313, "profile": 5676177281124120482, "path": 13047857732686095846, "deps": [[784494742817713399, "tower_service", false, 2984544939485805718], [1906322745568073236, "pin_project_lite", false, 16804108651755551674], [6264115378959545688, "pin_project", false, 3190792465183025036], [7620660491849607393, "futures_core", false, 7000057387335366342], [7712452662827335977, "tower_layer", false, 13504960368691367387], [8153389937262086537, "hdrhistogram", false, 13014065616895372658], [8606274917505247608, "tracing", false, 3170405985263080523], [10629569228670356391, "futures_util", false, 15962094945825603373], [13208667028893622512, "rand", false, 11109878790133241354], [14767213526276824509, "slab", false, 11104037078377016324], [14923790796823607459, "indexmap", false, 9168150013821689589], [15894030960229394068, "tokio_util", false, 18142039996363939408], [17531218394775549125, "tokio", false, 5325925400726759008]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tower-25997fa5a9c6ac56/dep-lib-tower", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}