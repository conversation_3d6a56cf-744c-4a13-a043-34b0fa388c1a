{"$message_type": "future_incompat", "future_incompat_report": [{"diagnostic": {"$message_type": "diagnostic", "message": "this function depends on never type fallback being `()`", "code": {"code": "dependency_on_unit_never_type_fallback", "explanation": null}, "level": "warning", "spans": [{"file_name": "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/connection/executor.rs", "byte_start": 707, "byte_end": 899, "line_start": 23, "line_end": 28, "column_start": 1, "column_end": 52, "is_primary": true, "text": [{"text": "async fn prepare(", "highlight_start": 1, "highlight_end": 18}, {"text": "    conn: &mut PgConnection,", "highlight_start": 1, "highlight_end": 29}, {"text": "    sql: &str,", "highlight_start": 1, "highlight_end": 15}, {"text": "    parameters: &[PgTypeInfo],", "highlight_start": 1, "highlight_end": 31}, {"text": "    metadata: Option<Arc<PgStatementMetadata>>,", "highlight_start": 1, "highlight_end": 48}, {"text": ") -> Result<(Oid, Arc<PgStatementMetadata>), Error> {", "highlight_start": 1, "highlight_end": 52}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [{"message": "this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!", "code": null, "level": "warning", "spans": [], "children": [], "rendered": null}, {"message": "for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "specify the types explicitly", "code": null, "level": "help", "spans": [], "children": [], "rendered": null}, {"message": "in edition 2024, the requirement `!: sqlx_core::io::Decode<'_>` will fail", "code": null, "level": "note", "spans": [{"file_name": "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/connection/executor.rs", "byte_start": 2149, "byte_end": 2160, "line_start": 68, "line_end": 68, "column_start": 10, "column_end": 21, "is_primary": true, "text": [{"text": "        .recv_expect(MessageFormat::ParseComplete)", "highlight_start": 10, "highlight_end": 21}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [], "rendered": null}, {"message": "use `()` annotations to avoid fallback changes", "code": null, "level": "help", "spans": [{"file_name": "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/connection/executor.rs", "byte_start": 2116, "byte_end": 2116, "line_start": 66, "line_end": 66, "column_start": 10, "column_end": 10, "is_primary": true, "text": [{"text": "    let _ = conn", "highlight_start": 10, "highlight_end": 10}], "label": null, "suggested_replacement": ": ()", "suggestion_applicability": "MachineApplicable", "expansion": null}], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this function depends on never type fallback being `()`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/connection/executor.rs:23:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m/\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn prepare(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    conn: &mut PgConnection,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    sql: &str,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    parameters: &[PgTypeInfo],\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    metadata: Option<Arc<PgStatementMetadata>>,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m) -> Result<(Oid, Arc<PgStatementMetadata>), Error> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|___________________________________________________^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: specify the types explicitly\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: in edition 2024, the requirement `!: sqlx_core::io::Decode<'_>` will fail\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/connection/executor.rs:68:10\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m68\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .recv_expect(MessageFormat::ParseComplete)\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `()` annotations to avoid fallback changes\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m66\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m    let _\u001b[0m\u001b[0m\u001b[38;5;10m: ()\u001b[0m\u001b[0m = conn\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[38;5;10m++++\u001b[0m\n\n"}}, {"diagnostic": {"$message_type": "diagnostic", "message": "this function depends on never type fallback being `()`", "code": {"code": "dependency_on_unit_never_type_fallback", "explanation": null}, "level": "warning", "spans": [{"file_name": "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs", "byte_start": 10362, "byte_end": 10428, "line_start": 262, "line_end": 262, "column_start": 5, "column_end": 71, "is_primary": true, "text": [{"text": "    pub async fn abort(mut self, msg: impl Into<String>) -> Result<()> {", "highlight_start": 5, "highlight_end": 71}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [{"message": "this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!", "code": null, "level": "warning", "spans": [], "children": [], "rendered": null}, {"message": "for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "specify the types explicitly", "code": null, "level": "help", "spans": [], "children": [], "rendered": null}, {"message": "in edition 2024, the requirement `!: sqlx_core::io::Decode<'_>` will fail", "code": null, "level": "note", "spans": [{"file_name": "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs", "byte_start": 11063, "byte_end": 11074, "line_start": 280, "line_end": 280, "column_start": 30, "column_end": 41, "is_primary": true, "text": [{"text": "                            .recv_expect(MessageFormat::ReadyForQuery)", "highlight_start": 30, "highlight_end": 41}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [], "rendered": null}, {"message": "use `()` annotations to avoid fallback changes", "code": null, "level": "help", "spans": [{"file_name": "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs", "byte_start": 11074, "byte_end": 11074, "line_start": 280, "line_end": 280, "column_start": 41, "column_end": 41, "is_primary": true, "text": [{"text": "                            .recv_expect(MessageFormat::ReadyForQuery)", "highlight_start": 41, "highlight_end": 41}], "label": null, "suggested_replacement": "::<()>", "suggestion_applicability": "MachineApplicable", "expansion": null}], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this function depends on never type fallback being `()`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs:262:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m262\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn abort(mut self, msg: impl Into<String>) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: specify the types explicitly\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: in edition 2024, the requirement `!: sqlx_core::io::Decode<'_>` will fail\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs:280:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m280\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m                   .recv_expect(MessageFormat::ReadyForQuery)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `()` annotations to avoid fallback changes\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m280\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m                            .recv_expect\u001b[0m\u001b[0m\u001b[38;5;10m::<()>\u001b[0m\u001b[0m(MessageFormat::ReadyForQuery)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[38;5;10m++++++\u001b[0m\n\n"}}, {"diagnostic": {"$message_type": "diagnostic", "message": "this function depends on never type fallback being `()`", "code": {"code": "dependency_on_unit_never_type_fallback", "explanation": null}, "level": "warning", "spans": [{"file_name": "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs", "byte_start": 11437, "byte_end": 11481, "line_start": 294, "line_end": 294, "column_start": 5, "column_end": 49, "is_primary": true, "text": [{"text": "    pub async fn finish(mut self) -> Result<u64> {", "highlight_start": 5, "highlight_end": 49}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [{"message": "this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!", "code": null, "level": "warning", "spans": [], "children": [], "rendered": null}, {"message": "for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "specify the types explicitly", "code": null, "level": "help", "spans": [], "children": [], "rendered": null}, {"message": "in edition 2024, the requirement `!: sqlx_core::io::Decode<'_>` will fail", "code": null, "level": "note", "spans": [{"file_name": "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs", "byte_start": 11993, "byte_end": 12004, "line_start": 314, "line_end": 314, "column_start": 14, "column_end": 25, "is_primary": true, "text": [{"text": "            .recv_expect(MessageFormat::ReadyForQuery)", "highlight_start": 14, "highlight_end": 25}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [], "rendered": null}, {"message": "use `()` annotations to avoid fallback changes", "code": null, "level": "help", "spans": [{"file_name": "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs", "byte_start": 12004, "byte_end": 12004, "line_start": 314, "line_end": 314, "column_start": 25, "column_end": 25, "is_primary": true, "text": [{"text": "            .recv_expect(MessageFormat::ReadyForQuery)", "highlight_start": 25, "highlight_end": 25}], "label": null, "suggested_replacement": "::<()>", "suggestion_applicability": "MachineApplicable", "expansion": null}], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this function depends on never type fallback being `()`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs:294:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m294\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn finish(mut self) -> Result<u64> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: specify the types explicitly\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: in edition 2024, the requirement `!: sqlx_core::io::Decode<'_>` will fail\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs:314:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m314\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .recv_expect(MessageFormat::ReadyForQuery)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `()` annotations to avoid fallback changes\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m314\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m            .recv_expect\u001b[0m\u001b[0m\u001b[38;5;10m::<()>\u001b[0m\u001b[0m(MessageFormat::ReadyForQuery)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[38;5;10m++++++\u001b[0m\n\n"}}, {"diagnostic": {"$message_type": "diagnostic", "message": "this function depends on never type fallback being `()`", "code": {"code": "dependency_on_unit_never_type_fallback", "explanation": null}, "level": "warning", "spans": [{"file_name": "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs", "byte_start": 12388, "byte_end": 12547, "line_start": 331, "line_end": 334, "column_start": 1, "column_end": 42, "is_primary": true, "text": [{"text": "async fn pg_begin_copy_out<'c, C: DerefMut<Target = PgConnection> + Send + 'c>(", "highlight_start": 1, "highlight_end": 80}, {"text": "    mut conn: C,", "highlight_start": 1, "highlight_end": 17}, {"text": "    statement: &str,", "highlight_start": 1, "highlight_end": 21}, {"text": ") -> Result<BoxStream<'c, Result<Bytes>>> {", "highlight_start": 1, "highlight_end": 42}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [{"message": "this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!", "code": null, "level": "warning", "spans": [], "children": [], "rendered": null}, {"message": "for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "specify the types explicitly", "code": null, "level": "help", "spans": [], "children": [], "rendered": null}, {"message": "in edition 2024, the requirement `!: sqlx_core::io::Decode<'_>` will fail", "code": null, "level": "note", "spans": [{"file_name": "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs", "byte_start": 13126, "byte_end": 13137, "line_start": 350, "line_end": 350, "column_start": 33, "column_end": 44, "is_primary": true, "text": [{"text": "                    conn.stream.recv_expect(MessageFormat::CommandComplete).await?;", "highlight_start": 33, "highlight_end": 44}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [], "rendered": null}, {"message": "use `()` annotations to avoid fallback changes", "code": null, "level": "help", "spans": [{"file_name": "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs", "byte_start": 13137, "byte_end": 13137, "line_start": 350, "line_end": 350, "column_start": 44, "column_end": 44, "is_primary": true, "text": [{"text": "                    conn.stream.recv_expect(MessageFormat::CommandComplete).await?;", "highlight_start": 44, "highlight_end": 44}], "label": null, "suggested_replacement": "::<()>", "suggestion_applicability": "MachineApplicable", "expansion": null}, {"file_name": "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs", "byte_start": 13221, "byte_end": 13221, "line_start": 351, "line_end": 351, "column_start": 44, "column_end": 44, "is_primary": true, "text": [{"text": "                    conn.stream.recv_expect(MessageFormat::ReadyForQuery).await?;", "highlight_start": 44, "highlight_end": 44}], "label": null, "suggested_replacement": "::<()>", "suggestion_applicability": "MachineApplicable", "expansion": null}], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this function depends on never type fallback being `()`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs:331:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m331\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m/\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn pg_begin_copy_out<'c, C: DerefMut<Target = PgConnection> + Send + 'c>(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m332\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    mut conn: C,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m333\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    statement: &str,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m334\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m) -> Result<BoxStream<'c, Result<Bytes>>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|_________________________________________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: specify the types explicitly\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: in edition 2024, the requirement `!: sqlx_core::io::Decode<'_>` will fail\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-postgres-0.7.4/src/copy.rs:350:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m350\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    conn.stream.recv_expect(MessageFormat::CommandComplete).await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `()` annotations to avoid fallback changes\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m350\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m                    conn.stream.recv_expect\u001b[0m\u001b[0m\u001b[38;5;10m::<()>\u001b[0m\u001b[0m(MessageFormat::CommandComplete).await?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m351\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m                    conn.stream.recv_expect\u001b[0m\u001b[0m\u001b[38;5;10m::<()>\u001b[0m\u001b[0m(MessageFormat::ReadyForQuery).await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}}]}