{"rustc": 12013579709055016942, "features": "[\"default\", \"rustls\", \"rustls-native-certs\"]", "declared_features": "[\"amq-protocol-codegen\", \"codegen\", \"codegen-internal\", \"default\", \"native-tls\", \"openssl\", \"rustls\", \"rustls-native-certs\", \"rustls-webpki-roots-certs\", \"serde_json\", \"vendored-openssl\"]", "target": 5408242616063297496, "profile": 17984201634715228204, "path": 14612029038518356852, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/lapin-073870f8cb71adc8/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}