{"rustc": 12013579709055016942, "features": "[\"color\", \"default\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 11332462681380664355, "path": 14952808061637982662, "deps": [[1608232316341851233, "clap_builder", false, 10145060413063050903], [9722254271889984554, "clap_derive", false, 7563352973688205658]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap-a840f3940fd19cc7/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}