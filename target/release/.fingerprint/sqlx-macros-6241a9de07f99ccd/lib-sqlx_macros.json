{"rustc": 12013579709055016942, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"postgres\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"time\", \"uuid\"]", "target": 13494433325021527976, "profile": 17984201634715228204, "path": 7923551923423982255, "deps": [[373107762698212489, "proc_macro2", false, 6976377392884279319], [996810380461694889, "sqlx_core", false, 15727449706319029976], [2713742371683562785, "syn", false, 8633771878724233584], [15733334431800349573, "sqlx_macros_core", false, 11830736641832624022], [17990358020177143287, "quote", false, 5460241503457564807]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/sqlx-macros-6241a9de07f99ccd/dep-lib-sqlx_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}