{"rustc": 12013579709055016942, "features": "[\"32-column-tables\", \"chrono\", \"default\", \"postgres\", \"with-deprecated\"]", "declared_features": "[\"128-column-tables\", \"32-column-tables\", \"64-column-tables\", \"chrono\", \"default\", \"mysql\", \"nightly\", \"postgres\", \"r2d2\", \"sqlite\", \"time\", \"with-deprecated\", \"without-deprecated\"]", "target": 14327538309307208008, "profile": 17984201634715228204, "path": 10128129705436944881, "deps": [[373107762698212489, "proc_macro2", false, 6976377392884279319], [9431064406736484046, "dsl_auto_type", false, 17005615793055971337], [12821317385041146439, "diesel_table_macro_syntax", false, 18387443770715456506], [17332570067994900305, "syn", false, 748840432555435317], [17990358020177143287, "quote", false, 5460241503457564807]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/diesel_derives-b7c587fc60c020f3/dep-lib-diesel_derives", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}