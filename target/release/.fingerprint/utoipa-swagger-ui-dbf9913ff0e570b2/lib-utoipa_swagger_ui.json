{"rustc": 12013579709055016942, "features": "[\"axum\"]", "declared_features": "[\"actix-web\", \"axum\", \"debug\", \"debug-embed\", \"rocket\"]", "target": 7413689917468982696, "profile": 5676177281124120482, "path": 14085025876312964636, "deps": [[2516681829802334884, "build_script_build", false, 11308430691565001005], [4352886507220678900, "serde_json", false, 13071536196104845035], [4891297352905791595, "axum", false, 3716890597299563734], [9689903380558560274, "serde", false, 13024165464168184619], [9979094739671224239, "rust_embed", false, 6330655040380312554], [17303498555858728463, "u<PERSON><PERSON>a", false, 14449284290636989553], [18071510856783138481, "mime_guess", false, 18031126515020272784]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/utoipa-swagger-ui-dbf9913ff0e570b2/dep-lib-utoipa_swagger_ui", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}