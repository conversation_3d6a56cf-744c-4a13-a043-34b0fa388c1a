{"rustc": 12013579709055016942, "features": "[\"default\"]", "declared_features": "[\"default\", \"has_bytes\", \"nats\", \"nats_storage\", \"postgres-native-tls\", \"postgres-openssl\", \"postgres_native_tls\", \"postgres_openssl\", \"postgres_storage\", \"prost\", \"prost-build\", \"signal\", \"tokio-postgres\", \"tracing-subscriber\"]", "target": 2318398449392625286, "profile": 5676177281124120482, "path": 6443413674247850361, "deps": [[156180212356192564, "build_script_build", false, 1410361006850260162], [2995469292676432503, "uuid", false, 17056657106244507279], [5157631553186200874, "num_traits", false, 12669158686331970551], [5990956534088275425, "num_derive", false, 11717965215811090894], [7294361402007043243, "cron", false, 10122571633632920129], [8606274917505247608, "tracing", false, 3170405985263080523], [9897246384292347999, "chrono", false, 5263219938714622616], [17531218394775549125, "tokio", false, 5325925400726759008]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tokio-cron-scheduler-3fe2cea46435eb9c/dep-lib-tokio_cron_scheduler", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}