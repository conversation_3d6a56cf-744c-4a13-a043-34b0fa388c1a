{"rustc": 12013579709055016942, "features": "[]", "declared_features": "[]", "target": 6169632844760826718, "profile": 5676177281124120482, "path": 9040463152832506689, "deps": [[2995469292676432503, "uuid", false, 17056657106244507279], [3722963349756955755, "once_cell", false, 16529270161116768813], [4352886507220678900, "serde_json", false, 13071536196104845035], [4405182208873388884, "http", false, 11584866034975155518], [6074040673074893728, "spdx", false, 6847723720820426169], [7929428804293058933, "packageurl", false, 7414367111831659286], [8008191657135824715, "thiserror", false, 5668788851826066514], [9451456094439810778, "regex", false, 9093491637583685841], [9689903380558560274, "serde", false, 13024165464168184619], [12409575957772518135, "time", false, 7358160977062045909], [14266040679612931110, "xml", false, 14846271881005570699], [18066890886671768183, "base64", false, 1329204773345504267]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/cyclonedx-bom-ac72540758a619f5/dep-lib-cyclonedx_bom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}