{"rustc": 12013579709055016942, "features": "[\"axum_extras\", \"chrono\", \"default\", \"uuid\"]", "declared_features": "[\"actix_extras\", \"auto_into_responses\", \"axum_extras\", \"chrono\", \"debug\", \"decimal\", \"decimal_float\", \"default\", \"indexmap\", \"non_strict_integers\", \"openapi_extensions\", \"preserve_order\", \"preserve_path_order\", \"rc_schema\", \"repr\", \"rocket_extras\", \"serde_yaml\", \"smallvec\", \"time\", \"ulid\", \"url\", \"uuid\", \"yaml\"]", "target": 2151970672667237190, "profile": 5676177281124120482, "path": 5884377034601637344, "deps": [[4352886507220678900, "serde_json", false, 13071536196104845035], [9285357129478606012, "indexmap", false, 4789598390882823638], [9689903380558560274, "serde", false, 13024165464168184619], [14477260817627552954, "utoipa_gen", false, 16469156040075152062]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/utoipa-38f21521a37123d1/dep-lib-utoipa", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}