{"rustc": 12013579709055016942, "features": "[\"tracing\"]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 5676177281124120482, "path": 16794120363744662192, "deps": [[784494742817713399, "tower_service", false, 2984544939485805718], [1906322745568073236, "pin_project_lite", false, 16804108651755551674], [2517136641825875337, "sync_wrapper", false, 2955055585461974833], [7712452662827335977, "tower_layer", false, 13504960368691367387], [8606274917505247608, "tracing", false, 3170405985263080523], [9010263965687315507, "http", false, 4105877743337224888], [10229185211513642314, "mime", false, 7924601984156595009], [10629569228670356391, "futures_util", false, 15962094945825603373], [14084095096285906100, "http_body", false, 10618577338403792607], [14156967978702956262, "rustversion", false, 7754313662399448305], [16066129441945555748, "bytes", false, 246911290282700904], [16611674984963787466, "async_trait", false, 8638216902090269789], [16900715236047033623, "http_body_util", false, 6694364142199478864]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/axum-core-4e5a1a4732db873e/dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}