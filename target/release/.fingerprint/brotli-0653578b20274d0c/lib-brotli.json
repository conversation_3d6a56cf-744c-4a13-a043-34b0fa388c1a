{"rustc": 12013579709055016942, "features": "[\"alloc-stdlib\", \"default\", \"std\"]", "declared_features": "[\"alloc-stdlib\", \"benchmark\", \"billing\", \"default\", \"disable-timer\", \"disallow_large_window_size\", \"external-literal-probability\", \"ffi-api\", \"float64\", \"floating_point_context_mixing\", \"no-stdlib-ffi-binding\", \"pass-through-ffi-panics\", \"seccomp\", \"sha2\", \"simd\", \"std\", \"validation\", \"vector_scratch_space\"]", "target": 8433163163091947982, "profile": 5676177281124120482, "path": 2311229697963263442, "deps": [[9611597350722197978, "alloc_no_stdlib", false, 6531984209524885870], [16413620717702030930, "brotli_decompressor", false, 4002512305278521315], [17470296833448545982, "alloc_stdlib", false, 17568106990599672605]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/brotli-0653578b20274d0c/dep-lib-brotli", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}