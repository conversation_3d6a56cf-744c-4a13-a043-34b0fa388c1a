{"rustc": 12013579709055016942, "features": "[\"brotli\", \"flate2\", \"gzip\", \"libzstd\", \"zlib\", \"zstd\", \"zstd-safe\"]", "declared_features": "[\"all-algorithms\", \"brotli\", \"bzip2\", \"deflate\", \"deflate64\", \"flate2\", \"gzip\", \"libzstd\", \"lz4\", \"lzma\", \"xz\", \"xz-parallel\", \"xz2\", \"zlib\", \"zstd\", \"zstd-safe\", \"zstdmt\"]", "target": 2807176193865957057, "profile": 5676177281124120482, "path": 734145910419238120, "deps": [[1273465285397585583, "compression_core", false, 8553306206435219658], [1678291836268844980, "brotli", false, 15855323099401032151], [1906322745568073236, "pin_project_lite", false, 16804108651755551674], [4052408954973158025, "libzstd", false, 9503633968836274382], [7620660491849607393, "futures_core", false, 7000057387335366342], [15788444815745660356, "zstd_safe", false, 8528816513367822442], [15932120279885307830, "memchr", false, 16801524134812060885], [17772299992546037086, "flate2", false, 8467521036819144465]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/compression-codecs-869a56f385786c79/dep-lib-compression_codecs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}