{"rustc": 12013579709055016942, "features": "[\"default\", \"rustls\", \"rustls-native-certs\"]", "declared_features": "[\"amq-protocol-codegen\", \"codegen\", \"codegen-internal\", \"default\", \"native-tls\", \"openssl\", \"rustls\", \"rustls-native-certs\", \"rustls-webpki-roots-certs\", \"serde_json\", \"vendored-openssl\"]", "target": 5047893427188600641, "profile": 5676177281124120482, "path": 9506836946184484854, "deps": [[5103565458935487, "futures_io", false, 1007513278623315838], [274451046250692236, "pinky_swear", false, 5113190881639826733], [1951382276944535576, "executor_trait", false, 14627683508163343548], [4495526598637097934, "parking_lot", false, 7224762146647036714], [4656928804077918400, "flume", false, 6929995297104852904], [4713603720635702932, "build_script_build", false, 14178498455368162634], [7048981225526245511, "amq_protocol", false, 8212142474261344917], [7620660491849607393, "futures_core", false, 7000057387335366342], [8606274917505247608, "tracing", false, 3170405985263080523], [8864093321401338808, "waker_fn", false, 3909296479747372641], [9689903380558560274, "serde", false, 13024165464168184619], [14332133141799632110, "reactor_trait", false, 13004642301806719362], [15121870802873242844, "async_global_executor_trait", false, 6961867959070835254], [16454787060997881635, "async_reactor_trait", false, 1828937542865305921], [16611674984963787466, "async_trait", false, 8638216902090269789]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/lapin-269874c85d230a0a/dep-lib-lapin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}