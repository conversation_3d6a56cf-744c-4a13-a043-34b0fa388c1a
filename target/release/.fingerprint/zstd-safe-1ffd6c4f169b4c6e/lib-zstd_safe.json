{"rustc": 12013579709055016942, "features": "[\"std\"]", "declared_features": "[\"arrays\", \"bindgen\", \"debug\", \"default\", \"doc-cfg\", \"experimental\", \"fat-lto\", \"legacy\", \"no_asm\", \"pkg-config\", \"seekable\", \"std\", \"thin\", \"thin-lto\", \"zdict_builder\", \"zstdmt\"]", "target": 13834647262792939399, "profile": 1273716162689127383, "path": 5108942767135810697, "deps": [[8373447648276846408, "zstd_sys", false, 7566725567931575572], [15788444815745660356, "build_script_build", false, 5285847521331066291]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/zstd-safe-1ffd6c4f169b4c6e/dep-lib-zstd_safe", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}