{"rustc": 12013579709055016942, "features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 14850331575045365232, "profile": 5676177281124120482, "path": 16888322143847190671, "deps": [[784494742817713399, "tower_service", false, 2984544939485805718], [1906322745568073236, "pin_project_lite", false, 16804108651755551674], [2995469292676432503, "uuid", false, 17056657106244507279], [3601586811267292532, "tower", false, 17805857618500045708], [4121350475192885151, "iri_string", false, 2029983387036298922], [6304235478050270880, "httpdate", false, 5037930442790668917], [6803352382179706244, "percent_encoding", false, 9365665524907602399], [7620660491849607393, "futures_core", false, 7000057387335366342], [7712452662827335977, "tower_layer", false, 13504960368691367387], [8606274917505247608, "tracing", false, 3170405985263080523], [9010263965687315507, "http", false, 4105877743337224888], [10229185211513642314, "mime", false, 7924601984156595009], [10629569228670356391, "futures_util", false, 15962094945825603373], [12475322156296016012, "http_range_header", false, 15801442175131385560], [14084095096285906100, "http_body", false, 10618577338403792607], [15840480199427237938, "bitflags", false, 7686821063439829817], [15894030960229394068, "tokio_util", false, 18142039996363939408], [16066129441945555748, "bytes", false, 246911290282700904], [16370133363440370004, "async_compression", false, 4637751169769466983], [16900715236047033623, "http_body_util", false, 6694364142199478864], [17531218394775549125, "tokio", false, 5325925400726759008], [18066890886671768183, "base64", false, 1329204773345504267], [18071510856783138481, "mime_guess", false, 18031126515020272784]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tower-http-3eccd795f8e313e4/dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}