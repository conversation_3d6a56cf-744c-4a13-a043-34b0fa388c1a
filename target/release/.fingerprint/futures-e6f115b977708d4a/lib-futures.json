{"rustc": 12013579709055016942, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 18134297140301713016, "path": 18331491783298331578, "deps": [[5103565458935487, "futures_io", false, 1007513278623315838], [1811549171721445101, "futures_channel", false, 948271069788412032], [7013762810557009322, "futures_sink", false, 15283880267278285427], [7620660491849607393, "futures_core", false, 7000057387335366342], [10629569228670356391, "futures_util", false, 15962094945825603373], [12779779637805422465, "futures_executor", false, 3847967213984390736], [16240732885093539806, "futures_task", false, 11477977833970993445]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/futures-e6f115b977708d4a/dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}