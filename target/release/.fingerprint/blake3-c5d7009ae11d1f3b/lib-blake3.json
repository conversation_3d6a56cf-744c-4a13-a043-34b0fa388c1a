{"rustc": 12013579709055016942, "features": "[\"default\", \"std\"]", "declared_features": "[\"default\", \"digest\", \"mmap\", \"neon\", \"no_avx2\", \"no_avx512\", \"no_neon\", \"no_sse2\", \"no_sse41\", \"prefer_intrinsics\", \"pure\", \"rayon\", \"serde\", \"std\", \"traits-preview\", \"wasm32_simd\", \"zeroize\"]", "target": 11963615372568355417, "profile": 5676177281124120482, "path": 8435792770864273055, "deps": [[1640307407508065381, "constant_time_eq", false, 251785194930605067], [7843059260364151289, "cfg_if", false, 3788051679790835991], [9241925498456048256, "build_script_build", false, 15279261797200273345], [9529943735784919782, "arrayref", false, 8419956431109366598], [13847662864258534762, "arrayvec", false, 15434534241684916946]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/blake3-c5d7009ae11d1f3b/dep-lib-blake3", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}