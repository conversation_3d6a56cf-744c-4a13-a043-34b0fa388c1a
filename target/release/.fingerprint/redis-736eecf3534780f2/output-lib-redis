{"$message_type": "future_incompat", "future_incompat_report": [{"diagnostic": {"$message_type": "diagnostic", "message": "this function depends on never type fallback being `()`", "code": {"code": "dependency_on_unit_never_type_fallback", "explanation": null}, "level": "warning", "spans": [{"file_name": "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/redis-0.24.0/src/script.rs", "byte_start": 4716, "byte_end": 4861, "line_start": 162, "line_end": 165, "column_start": 5, "column_end": 27, "is_primary": true, "text": [{"text": "    pub async fn invoke_async<C, T>(&self, con: &mut C) -> RedisResult<T>", "highlight_start": 5, "highlight_end": 74}, {"text": "    where", "highlight_start": 1, "highlight_end": 10}, {"text": "        C: crate::aio::ConnectionLike,", "highlight_start": 1, "highlight_end": 39}, {"text": "        T: FromRedisValue,", "highlight_start": 1, "highlight_end": 27}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [{"message": "this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!", "code": null, "level": "warning", "spans": [], "children": [], "rendered": null}, {"message": "for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "specify the types explicitly", "code": null, "level": "help", "spans": [], "children": [], "rendered": null}, {"message": "in edition 2024, the requirement `!: FromRedisValue` will fail", "code": null, "level": "note", "spans": [{"file_name": "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/redis-0.24.0/src/script.rs", "byte_start": 5290, "byte_end": 5301, "line_start": 176, "line_end": 176, "column_start": 37, "column_end": 48, "is_primary": true, "text": [{"text": "                    self.load_cmd().query_async(con).await?;", "highlight_start": 37, "highlight_end": 48}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [], "rendered": null}, {"message": "use `()` annotations to avoid fallback changes", "code": null, "level": "help", "spans": [{"file_name": "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/redis-0.24.0/src/script.rs", "byte_start": 5301, "byte_end": 5301, "line_start": 176, "line_end": 176, "column_start": 48, "column_end": 48, "is_primary": true, "text": [{"text": "                    self.load_cmd().query_async(con).await?;", "highlight_start": 48, "highlight_end": 48}], "label": null, "suggested_replacement": "::<_, ()>", "suggestion_applicability": "MachineApplicable", "expansion": null}], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this function depends on never type fallback being `()`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/redis-0.24.0/src/script.rs:162:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m162\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn invoke_async<C, T>(&self, con: &mut C) -> RedisResult<T>\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m163\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m164\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        C: crate::aio::ConnectionLike,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m165\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        T: FromRedisValue,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|__________________________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: specify the types explicitly\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: in edition 2024, the requirement `!: FromRedisValue` will fail\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/redis-0.24.0/src/script.rs:176:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m176\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    self.load_cmd().query_async(con).await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `()` annotations to avoid fallback changes\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m176\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m                    self.load_cmd().query_async\u001b[0m\u001b[0m\u001b[38;5;10m::<_, ()>\u001b[0m\u001b[0m(con).await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                \u001b[0m\u001b[0m\u001b[38;5;10m+++++++++\u001b[0m\n\n"}}, {"diagnostic": {"$message_type": "diagnostic", "message": "this function depends on never type fallback being `()`", "code": {"code": "dependency_on_unit_never_type_fallback", "explanation": null}, "level": "warning", "spans": [{"file_name": "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/redis-0.24.0/src/script.rs", "byte_start": 4128, "byte_end": 4215, "line_start": 144, "line_end": 144, "column_start": 5, "column_end": 92, "is_primary": true, "text": [{"text": "    pub fn invoke<T: FromRedisValue>(&self, con: &mut dyn ConnectionLike) -> RedisResult<T> {", "highlight_start": 5, "highlight_end": 92}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [{"message": "this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!", "code": null, "level": "warning", "spans": [], "children": [], "rendered": null}, {"message": "for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "specify the types explicitly", "code": null, "level": "help", "spans": [], "children": [], "rendered": null}, {"message": "in edition 2024, the requirement `!: FromRedisValue` will fail", "code": null, "level": "note", "spans": [{"file_name": "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/redis-0.24.0/src/script.rs", "byte_start": 4448, "byte_end": 4453, "line_start": 150, "line_end": 150, "column_start": 37, "column_end": 42, "is_primary": true, "text": [{"text": "                    self.load_cmd().query(con)?;", "highlight_start": 37, "highlight_end": 42}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [], "rendered": null}, {"message": "use `()` annotations to avoid fallback changes", "code": null, "level": "help", "spans": [{"file_name": "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/redis-0.24.0/src/script.rs", "byte_start": 4453, "byte_end": 4453, "line_start": 150, "line_end": 150, "column_start": 42, "column_end": 42, "is_primary": true, "text": [{"text": "                    self.load_cmd().query(con)?;", "highlight_start": 42, "highlight_end": 42}], "label": null, "suggested_replacement": "::<()>", "suggestion_applicability": "MachineApplicable", "expansion": null}], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this function depends on never type fallback being `()`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/redis-0.24.0/src/script.rs:144:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m144\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn invoke<T: FromRedisValue>(&self, con: &mut dyn ConnectionLike) -> RedisResult<T> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: specify the types explicitly\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: in edition 2024, the requirement `!: FromRedisValue` will fail\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/redis-0.24.0/src/script.rs:150:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m150\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    self.load_cmd().query(con)?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `()` annotations to avoid fallback changes\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m150\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m                    self.load_cmd().query\u001b[0m\u001b[0m\u001b[38;5;10m::<()>\u001b[0m\u001b[0m(con)?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[38;5;10m++++++\u001b[0m\n\n"}}]}