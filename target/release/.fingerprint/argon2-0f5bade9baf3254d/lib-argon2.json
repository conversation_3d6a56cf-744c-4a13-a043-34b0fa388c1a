{"rustc": 12013579709055016942, "features": "[\"alloc\", \"default\", \"password-hash\", \"rand\"]", "declared_features": "[\"alloc\", \"default\", \"password-hash\", \"rand\", \"simple\", \"std\", \"zeroize\"]", "target": 5931530492013982456, "profile": 5676177281124120482, "path": 13148275687950099818, "deps": [[6742268975477224606, "password_hash", false, 1060705826157561154], [8700459469608572718, "blake2", false, 15363640903544723382], [13036989088902834928, "base64ct", false, 11843848546456789137], [17620084158052398167, "cpufeatures", false, 9212253482419482797]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/argon2-0f5bade9baf3254d/dep-lib-argon2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}