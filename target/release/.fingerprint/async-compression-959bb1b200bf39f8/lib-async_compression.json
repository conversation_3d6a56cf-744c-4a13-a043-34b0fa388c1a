{"rustc": 12013579709055016942, "features": "[\"brotli\", \"flate2\", \"gzip\", \"libzstd\", \"tokio\", \"zlib\", \"zstd\", \"zstd-safe\"]", "declared_features": "[\"all\", \"all-algorithms\", \"all-implementations\", \"brotli\", \"bzip2\", \"deflate\", \"deflate64\", \"flate2\", \"futures-io\", \"gzip\", \"liblzma\", \"libzstd\", \"lz4\", \"lzma\", \"tokio\", \"xz\", \"xz-parallel\", \"xz2\", \"zlib\", \"zstd\", \"zstd-safe\", \"zstdmt\"]", "target": 7068030942456847288, "profile": 5676177281124120482, "path": 13697574667197981824, "deps": [[1273465285397585583, "compression_core", false, 8553306206435219658], [1678291836268844980, "brotli", false, 15855323099401032151], [1906322745568073236, "pin_project_lite", false, 16804108651755551674], [4052408954973158025, "libzstd", false, 9503633968836274382], [7620660491849607393, "futures_core", false, 7000057387335366342], [12749330565809609630, "compression_codecs", false, 7645926938528337700], [15788444815745660356, "zstd_safe", false, 8528816513367822442], [15932120279885307830, "memchr", false, 16801524134812060885], [17531218394775549125, "tokio", false, 5325925400726759008], [17772299992546037086, "flate2", false, 8467521036819144465]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/async-compression-959bb1b200bf39f8/dep-lib-async_compression", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}