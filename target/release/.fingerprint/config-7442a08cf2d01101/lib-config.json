{"rustc": 12013579709055016942, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 10937020577594238424, "path": 11287088179959831649, "deps": [[1213098572879462490, "json5_rs", false, 6817372039596878861], [1965680986145237447, "yaml_rust2", false, 5414929903542423028], [2244620803250265856, "ron", false, 3309511242881455072], [4352886507220678900, "serde_json", false, 13071536196104845035], [6502365400774175331, "nom", false, 1839500977407781021], [6517602928339163454, "path<PERSON><PERSON>", false, 7225996408356358961], [9689903380558560274, "serde", false, 13024165464168184619], [13475460906694513802, "convert_case", false, 12166392883092672396], [14618892375165583068, "ini", false, 8355106075279897003], [15609422047640926750, "toml", false, 9916438302740077441], [16611674984963787466, "async_trait", false, 8638216902090269789]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/config-7442a08cf2d01101/dep-lib-config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}