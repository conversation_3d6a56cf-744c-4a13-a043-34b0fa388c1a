{"rustc": 12013579709055016942, "features": "[\"32-column-tables\", \"chrono\", \"default\", \"postgres\", \"postgres_backend\", \"uuid\", \"with-deprecated\"]", "declared_features": "[\"128-column-tables\", \"32-column-tables\", \"64-column-tables\", \"__with_asan_tests\", \"chrono\", \"default\", \"extras\", \"huge-tables\", \"i-implement-a-third-party-backend-and-opt-into-breaking-changes\", \"ipnet-address\", \"large-tables\", \"mysql\", \"mysql_backend\", \"mysqlclient-src\", \"network-address\", \"numeric\", \"postgres\", \"postgres_backend\", \"pq-src\", \"quickcheck\", \"r2d2\", \"returning_clauses_for_sqlite_3_35\", \"serde_json\", \"sqlite\", \"time\", \"unstable\", \"uuid\", \"with-deprecated\", \"without-deprecated\"]", "target": 17967542459835189317, "profile": 5676177281124120482, "path": 3642724647083795508, "deps": [[2995469292676432503, "uuid", false, 17056657106244507279], [3712811570531045576, "byteorder", false, 347336196719434964], [7695812897323945497, "itoa", false, 7312472855760296476], [9897246384292347999, "chrono", false, 5263219938714622616], [10386784435182696946, "pq_sys", false, 2551959520767069799], [15840480199427237938, "bitflags", false, 7686821063439829817], [17810152143764662619, "diesel_derives", false, 3082082020627814061]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/diesel-23ee84866d1fd2b1/dep-lib-diesel", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}