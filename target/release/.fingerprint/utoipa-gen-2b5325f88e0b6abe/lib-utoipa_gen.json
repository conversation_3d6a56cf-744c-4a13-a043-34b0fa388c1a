{"rustc": 12013579709055016942, "features": "[\"axum_extras\", \"chrono\", \"regex\", \"uuid\"]", "declared_features": "[\"actix_extras\", \"auto_into_responses\", \"axum_extras\", \"chrono\", \"debug\", \"decimal\", \"decimal_float\", \"indexmap\", \"non_strict_integers\", \"rc_schema\", \"regex\", \"repr\", \"rocket_extras\", \"smallvec\", \"time\", \"ulid\", \"url\", \"uuid\", \"yaml\"]", "target": 7060440504916948280, "profile": 17984201634715228204, "path": 699819569972644015, "deps": [[248545985466586061, "proc_macro_error", false, 4117136957853692730], [373107762698212489, "proc_macro2", false, 6976377392884279319], [2995469292676432503, "uuid", false, 2907181219260073841], [9451456094439810778, "regex", false, 7503955277376505467], [17332570067994900305, "syn", false, 748840432555435317], [17990358020177143287, "quote", false, 5460241503457564807]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/utoipa-gen-2b5325f88e0b6abe/dep-lib-utoipa_gen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}