{"rustc": 12013579709055016942, "features": "[\"alloc\", \"bytes\", \"futures-core-03\", \"pin-project-lite\", \"std\", \"tokio\", \"tokio-dep\", \"tokio-util\"]", "declared_features": "[\"alloc\", \"bytes\", \"bytes_05\", \"default\", \"futures-03\", \"futures-core-03\", \"futures-io-03\", \"mp4\", \"pin-project\", \"pin-project-lite\", \"regex\", \"std\", \"tokio\", \"tokio-02\", \"tokio-02-dep\", \"tokio-03\", \"tokio-03-dep\", \"tokio-dep\", \"tokio-util\"]", "target": 2090804380371586739, "profile": 5676177281124120482, "path": 6170777391724605029, "deps": [[1906322745568073236, "pin_project_lite", false, 16804108651755551674], [7620660491849607393, "futures_core_03", false, 7000057387335366342], [15894030960229394068, "tokio_util", false, 18142039996363939408], [15932120279885307830, "memchr", false, 16801524134812060885], [16066129441945555748, "bytes", false, 246911290282700904], [17531218394775549125, "tokio_dep", false, 5325925400726759008]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/combine-00cec0767a977074/dep-lib-combine", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}