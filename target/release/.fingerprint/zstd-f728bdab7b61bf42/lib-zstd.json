{"rustc": 12013579709055016942, "features": "[]", "declared_features": "[\"arrays\", \"bindgen\", \"debug\", \"default\", \"doc-cfg\", \"experimental\", \"fat-lto\", \"legacy\", \"no_asm\", \"pkg-config\", \"thin\", \"thin-lto\", \"wasm\", \"zdict_builder\", \"zstdmt\"]", "target": 13967053409313941148, "profile": 5676177281124120482, "path": 6929034755815885365, "deps": [[15788444815745660356, "zstd_safe", false, 8528816513367822442]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/zstd-f728bdab7b61bf42/dep-lib-zstd", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}