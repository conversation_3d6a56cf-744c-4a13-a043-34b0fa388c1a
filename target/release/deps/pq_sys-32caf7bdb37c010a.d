/home/<USER>/Documents/augment-projects/infinitium-signal/target/release/deps/pq_sys-32caf7bdb37c010a.d: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/pq-sys-0.7.2/src/lib.rs /home/<USER>/Documents/augment-projects/infinitium-signal/target/release/build/pq-sys-96dddea799ace6e7/out/bindings.rs

/home/<USER>/Documents/augment-projects/infinitium-signal/target/release/deps/libpq_sys-32caf7bdb37c010a.rlib: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/pq-sys-0.7.2/src/lib.rs /home/<USER>/Documents/augment-projects/infinitium-signal/target/release/build/pq-sys-96dddea799ace6e7/out/bindings.rs

/home/<USER>/Documents/augment-projects/infinitium-signal/target/release/deps/libpq_sys-32caf7bdb37c010a.rmeta: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/pq-sys-0.7.2/src/lib.rs /home/<USER>/Documents/augment-projects/infinitium-signal/target/release/build/pq-sys-96dddea799ace6e7/out/bindings.rs

/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/pq-sys-0.7.2/src/lib.rs:
/home/<USER>/Documents/augment-projects/infinitium-signal/target/release/build/pq-sys-96dddea799ace6e7/out/bindings.rs:

# env-dep:OUT_DIR=/home/<USER>/Documents/augment-projects/infinitium-signal/target/release/build/pq-sys-96dddea799ace6e7/out
