use crate::{
    api::{ApiResponse, AppState},
    error::Result,
};
use axum::{
    extract::State,
    http::StatusCode,
    response::Json,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{info, warn};

/// Health check response
#[derive(Debug, Serialize, Deserialize, utoipa::ToSchema)]
pub struct HealthResponse {
    /// Overall health status
    pub status: HealthStatus,
    /// Timestamp of the health check
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// Version information
    pub version: String,
    /// Individual service health checks
    pub services: HashMap<String, ServiceHealth>,
    /// System metrics
    pub metrics: SystemMetrics,
}

/// Health status enumeration
#[derive(Debug, Clone, Serialize, Deserialize, utoipa::ToSchema)]
#[serde(rename_all = "lowercase")]
pub enum HealthStatus {
    /// All services are healthy
    Healthy,
    /// Some services are degraded but system is operational
    Degraded,
    /// Critical services are down
    Unhealthy,
}

/// Individual service health
#[derive(Debug, Serialize, Deserialize, utoipa::ToSchema)]
pub struct ServiceHealth {
    /// Service health status
    pub status: HealthStatus,
    /// Last check timestamp
    pub last_check: chrono::DateTime<chrono::Utc>,
    /// Response time in milliseconds
    pub response_time_ms: u64,
    /// Additional service-specific details
    pub details: Option<serde_json::Value>,
    /// Error message if unhealthy
    pub error: Option<String>,
}

/// System metrics
#[derive(Debug, Serialize, Deserialize, utoipa::ToSchema)]
pub struct SystemMetrics {
    /// Memory usage in bytes
    pub memory_usage_bytes: u64,
    /// CPU usage percentage
    pub cpu_usage_percent: f64,
    /// Disk usage percentage
    pub disk_usage_percent: f64,
    /// Number of active connections
    pub active_connections: u32,
    /// Uptime in seconds
    pub uptime_seconds: u64,
}

/// Kubernetes liveness probe endpoint
/// 
/// This endpoint checks if the application is alive and should be restarted if it fails.
/// It performs minimal checks to ensure the application is responsive.
#[utoipa::path(
    get,
    path = "/health/live",
    responses(
        (status = 200, description = "Application is alive", body = HealthResponse),
        (status = 503, description = "Application is not responding")
    ),
    tag = "Health"
)]
pub async fn liveness_probe(
    State(state): State<AppState>,
) -> std::result::Result<Json<ApiResponse<HealthResponse>>, StatusCode> {
    info!("Liveness probe check");

    let start_time = std::time::Instant::now();
    
    // Basic application responsiveness check
    let health_response = HealthResponse {
        status: HealthStatus::Healthy,
        timestamp: chrono::Utc::now(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        services: HashMap::new(),
        metrics: SystemMetrics {
            memory_usage_bytes: 0, // Simplified for liveness
            cpu_usage_percent: 0.0,
            disk_usage_percent: 0.0,
            active_connections: 0,
            uptime_seconds: start_time.elapsed().as_secs(),
        },
    };

    Ok(Json(ApiResponse::success(health_response)))
}

/// Kubernetes readiness probe endpoint
/// 
/// This endpoint checks if the application is ready to receive traffic.
/// It performs comprehensive checks of all dependencies.
#[utoipa::path(
    get,
    path = "/health/ready",
    responses(
        (status = 200, description = "Application is ready", body = HealthResponse),
        (status = 503, description = "Application is not ready")
    ),
    tag = "Health"
)]
pub async fn readiness_probe(
    State(state): State<AppState>,
) -> std::result::Result<Json<ApiResponse<HealthResponse>>, StatusCode> {
    info!("Readiness probe check");

    let start_time = std::time::Instant::now();
    let mut services = HashMap::new();
    let mut overall_status = HealthStatus::Healthy;

    // Check database connectivity (only affects status if database is enabled)
    let db_health = check_database_health(&state).await;
    if state.database.is_some() {
        // Only consider database health if database is enabled
        if matches!(db_health.status, HealthStatus::Unhealthy) {
            overall_status = HealthStatus::Unhealthy;
        } else if matches!(db_health.status, HealthStatus::Degraded) && matches!(overall_status, HealthStatus::Healthy) {
            overall_status = HealthStatus::Degraded;
        }
    }
    services.insert("database".to_string(), db_health);

    // Check Redis connectivity
    let redis_health = check_redis_health(&state).await;
    if matches!(redis_health.status, HealthStatus::Unhealthy) {
        overall_status = HealthStatus::Unhealthy;
    } else if matches!(redis_health.status, HealthStatus::Degraded) && matches!(overall_status, HealthStatus::Healthy) {
        overall_status = HealthStatus::Degraded;
    }
    services.insert("redis".to_string(), redis_health);

    // Check external services
    let external_health = check_external_services(&state).await;
    if matches!(external_health.status, HealthStatus::Degraded) && matches!(overall_status, HealthStatus::Healthy) {
        overall_status = HealthStatus::Degraded;
    }
    services.insert("external_services".to_string(), external_health);

    let health_response = HealthResponse {
        status: overall_status.clone(),
        timestamp: chrono::Utc::now(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        services,
        metrics: get_system_metrics().await,
    };

    match overall_status {
        HealthStatus::Healthy => Ok(Json(ApiResponse::success(health_response))),
        HealthStatus::Degraded => {
            warn!("Application is degraded but operational");
            Ok(Json(ApiResponse::success(health_response)))
        }
        HealthStatus::Unhealthy => {
            warn!("Application is unhealthy");
            Err(StatusCode::SERVICE_UNAVAILABLE)
        }
    }
}

/// Comprehensive health check endpoint
#[utoipa::path(
    get,
    path = "/health",
    responses(
        (status = 200, description = "Detailed health information", body = HealthResponse),
        (status = 503, description = "Service unavailable")
    ),
    tag = "Health"
)]
pub async fn health_check(
    State(state): State<AppState>,
) -> std::result::Result<Json<ApiResponse<HealthResponse>>, StatusCode> {
    // Reuse readiness probe logic for comprehensive health check
    readiness_probe(State(state)).await
}

/// Check database health
async fn check_database_health(state: &AppState) -> ServiceHealth {
    let start_time = std::time::Instant::now();

    match &state.database {
        Some(database) => {
            match database.health_check().await {
                Ok(db_health) => ServiceHealth {
                    status: if db_health.connected { HealthStatus::Healthy } else { HealthStatus::Unhealthy },
                    last_check: chrono::Utc::now(),
                    response_time_ms: start_time.elapsed().as_millis() as u64,
                    details: Some(serde_json::json!({
                        "connected": db_health.connected,
                        "version": db_health.version,
                        "total_connections": db_health.pool_stats.total_connections,
                        "active_connections": db_health.pool_stats.active_connections,
                        "idle_connections": db_health.pool_stats.idle_connections,
                        "max_connections": db_health.pool_stats.max_connections
                    })),
                    error: None,
                },
                Err(e) => ServiceHealth {
                    status: HealthStatus::Unhealthy,
                    last_check: chrono::Utc::now(),
                    response_time_ms: start_time.elapsed().as_millis() as u64,
                    details: None,
                    error: Some(e.to_string()),
                },
            }
        }
        None => ServiceHealth {
            status: HealthStatus::Healthy, // Demo mode is considered healthy
            last_check: chrono::Utc::now(),
            response_time_ms: start_time.elapsed().as_millis() as u64,
            details: Some(serde_json::json!({
                "mode": "demo",
                "database_enabled": false,
                "message": "Running in demo mode without database"
            })),
            error: None,
        },
    }
}

/// Check Redis health
async fn check_redis_health(state: &AppState) -> ServiceHealth {
    let start_time = std::time::Instant::now();
    
    // For now, assume Redis is healthy if configured
    // TODO: Implement actual Redis health check when Redis client is available
    ServiceHealth {
        status: HealthStatus::Healthy,
        last_check: chrono::Utc::now(),
        response_time_ms: start_time.elapsed().as_millis() as u64,
        details: Some(serde_json::json!({
            "status": "configured"
        })),
        error: None,
    }
}

/// Check external services health
async fn check_external_services(state: &AppState) -> ServiceHealth {
    let start_time = std::time::Instant::now();
    
    // Check if external services are reachable
    // For now, return healthy status
    // TODO: Implement actual external service health checks
    ServiceHealth {
        status: HealthStatus::Healthy,
        last_check: chrono::Utc::now(),
        response_time_ms: start_time.elapsed().as_millis() as u64,
        details: Some(serde_json::json!({
            "nvd_api": "configured",
            "github_api": "configured",
            "snyk_api": "configured"
        })),
        error: None,
    }
}

/// Get system metrics
async fn get_system_metrics() -> SystemMetrics {
    // TODO: Implement actual system metrics collection
    // For now, return placeholder values
    SystemMetrics {
        memory_usage_bytes: 0,
        cpu_usage_percent: 0.0,
        disk_usage_percent: 0.0,
        active_connections: 0,
        uptime_seconds: 0,
    }
}
